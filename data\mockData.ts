import { Lead, OpenHouse, Message, Document, Folder, User, Notification, Activity } from '../types';

export const mockLeads: Lead[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    status: 'qualified',
    source: 'Website',
    propertyInterest: '3BR Condo Downtown',
    budget: 450000,
    lastContact: '2025-01-25',
    nextAction: 'Schedule property viewing',
    assignedAgent: '<PERSON>',
    createdAt: '2025-01-20',
    notes: 'Looking for move-in ready property'
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    status: 'new',
    source: 'Referral',
    propertyInterest: '4BR House Suburbs',
    budget: 650000,
    lastContact: '2025-01-26',
    nextAction: 'Initial consultation call',
    assignedAgent: '<PERSON>',
    createdAt: '2025-01-26',
    notes: 'First-time home buyer'
  },
  {
    id: '3',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '(*************',
    status: 'contacted',
    source: 'Facebook Ad',
    propertyInterest: '2BR Apartment',
    budget: 300000,
    lastContact: '2025-01-24',
    nextAction: 'Send property listings',
    assignedAgent: 'Sarah Johnson',
    createdAt: '2025-01-22',
    notes: 'Interested in modern amenities'
  }
];

export const mockOpenHouses: OpenHouse[] = [
  {
    id: '1',
    title: 'Modern Downtown Condo',
    propertyAddress: '123 Main St, Downtown',
    date: '2025-01-30',
    startTime: '14:00',
    endTime: '16:00',
    agent: 'Sarah Johnson',
    attendees: 12,
    status: 'scheduled',
    description: 'Luxury 3BR condo with city views'
  },
  {
    id: '2',
    title: 'Family Home in Suburbs',
    propertyAddress: '456 Oak Ave, Suburbs',
    date: '2025-02-01',
    startTime: '10:00',
    endTime: '12:00',
    agent: 'Mike Wilson',
    attendees: 8,
    status: 'scheduled',
    description: '4BR house with large backyard'
  },
  {
    id: '3',
    title: 'Starter Home',
    propertyAddress: '789 Pine St, Eastside',
    date: '2025-01-28',
    startTime: '13:00',
    endTime: '15:00',
    agent: 'Sarah Johnson',
    attendees: 15,
    status: 'completed',
    description: '2BR house perfect for first-time buyers'
  }
];

export const mockMessages: Message[] = [
  {
    id: '1',
    senderId: 'agent1',
    recipientId: '1',
    content: 'Hi John, I found some properties that match your criteria. Would you like to schedule a viewing?',
    timestamp: '2025-01-26T10:30:00Z',
    type: 'text',
    isRead: true
  },
  {
    id: '2',
    senderId: '1',
    recipientId: 'agent1',
    content: 'Yes, that sounds great! I\'m available this weekend.',
    timestamp: '2025-01-26T11:15:00Z',
    type: 'text',
    isRead: true
  },
  {
    id: '3',
    senderId: 'agent1',
    recipientId: '2',
    content: 'Welcome to our service! I\'ll be your agent. Let\'s set up a consultation call.',
    timestamp: '2025-01-26T09:00:00Z',
    type: 'text',
    isRead: false
  }
];

export const mockDocuments: Document[] = [
  {
    id: '1',
    name: 'Property_Agreement_John_Smith.pdf',
    type: 'pdf',
    size: 245760,
    uploadedAt: '2025-01-25T14:20:00Z',
    leadId: '1',
    url: '#'
  },
  {
    id: '2',
    name: 'Property_Photos_Downtown_Condo.zip',
    type: 'zip',
    size: 15728640,
    uploadedAt: '2025-01-24T16:45:00Z',
    url: '#'
  },
  {
    id: '3',
    name: 'Market_Analysis_Q1_2025.xlsx',
    type: 'xlsx',
    size: 524288,
    uploadedAt: '2025-01-26T11:30:00Z',
    folderId: 'folder1',
    url: '#'
  }
];

export const mockFolders: Folder[] = [
  {
    id: 'folder1',
    name: 'Market Reports',
    createdAt: '2025-01-20T10:00:00Z'
  },
  {
    id: 'folder2',
    name: 'Client Documents',
    createdAt: '2025-01-22T14:30:00Z'
  },
  {
    id: 'folder3',
    name: 'Property Photos',
    createdAt: '2025-01-18T09:15:00Z'
  }
];

export const mockUser: User = {
  id: 'user1',
  name: 'Sarah Johnson',
  email: '<EMAIL>',
  role: 'agent',
  avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b55c?w=150&h=150&fit=crop&crop=face'
};

export const mockNotifications: Notification[] = [
  {
    id: '1',
    title: 'New Lead',
    message: 'Emily Davis submitted an inquiry',
    type: 'info',
    timestamp: '2025-01-26T12:00:00Z',
    isRead: false
  },
  {
    id: '2',
    title: 'Open House Reminder',
    message: 'Downtown Condo open house starts in 2 hours',
    type: 'warning',
    timestamp: '2025-01-26T12:00:00Z',
    isRead: false
  },
  {
    id: '3',
    title: 'Document Uploaded',
    message: 'Property agreement signed by John Smith',
    type: 'success',
    timestamp: '2025-01-25T14:20:00Z',
    isRead: true
  }
];

export const mockActivities: Activity[] = [
  {
    id: '1',
    type: 'lead-added',
    description: 'New lead Emily Davis added from referral',
    timestamp: '2025-01-26T12:00:00Z',
    userId: 'user1',
    relatedId: '2'
  },
  {
    id: '2',
    type: 'open-house-scheduled',
    description: 'Open house scheduled for Downtown Condo',
    timestamp: '2025-01-25T15:30:00Z',
    userId: 'user1',
    relatedId: '1'
  },
  {
    id: '3',
    type: 'message-sent',
    description: 'Message sent to John Smith about property viewing',
    timestamp: '2025-01-26T10:30:00Z',
    userId: 'user1',
    relatedId: '1'
  },
  {
    id: '4',
    type: 'document-uploaded',
    description: 'Property agreement uploaded for John Smith',
    timestamp: '2025-01-25T14:20:00Z',
    userId: 'user1',
    relatedId: '1'
  }
];

export const mockMetrics = {
  totalLeads: 147,
  openHousesThisWeek: 5,
  pendingTasks: 12,
  revenuePipeline: 2450000
};