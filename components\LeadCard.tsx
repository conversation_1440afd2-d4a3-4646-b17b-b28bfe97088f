import { Lead } from '../types';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { Badge } from './ui/badge';
import { MoreHorizontal, Phone, Mail, Calendar } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from './ui/dropdown-menu';

interface LeadCardProps {
  lead: Lead;
  onStatusChange: (leadId: string, newStatus: Lead['status']) => void;
  onEdit: (leadId: string) => void;
  onDelete: (leadId: string) => void;
}

export function LeadCard({ lead, onStatusChange, onEdit, onDelete }: LeadCardProps) {
  const getStatusColor = (status: Lead['status']) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'contacted': return 'bg-yellow-100 text-yellow-800';
      case 'qualified': return 'bg-green-100 text-green-800';
      case 'closed-won': return 'bg-green-500 text-white';
      case 'closed-lost': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <Card className="p-4 hover:shadow-md transition-shadow">
      <CardContent className="p-0">
        <div className="flex items-start justify-between mb-3">
          <div>
            <h3 className="font-medium">{lead.name}</h3>
            <p className="text-sm text-muted-foreground">{lead.email}</p>
            <p className="text-sm text-muted-foreground">{lead.phone}</p>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getStatusColor(lead.status)} variant="secondary">
              {lead.status.replace('-', ' ')}
            </Badge>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => onEdit(lead.id)}>
                  Edit
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onStatusChange(lead.id, 'contacted')}>
                  Mark as Contacted
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onStatusChange(lead.id, 'qualified')}>
                  Mark as Qualified
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={() => onDelete(lead.id)}
                  className="text-destructive"
                >
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        
        <div className="space-y-2 mb-3">
          <div className="text-sm">
            <span className="text-muted-foreground">Interest:</span> {lead.propertyInterest}
          </div>
          <div className="text-sm">
            <span className="text-muted-foreground">Budget:</span> {formatCurrency(lead.budget)}
          </div>
          <div className="text-sm">
            <span className="text-muted-foreground">Last Contact:</span> {new Date(lead.lastContact).toLocaleDateString()}
          </div>
          <div className="text-sm">
            <span className="text-muted-foreground">Next Action:</span> {lead.nextAction}
          </div>
        </div>

        <div className="flex gap-2">
          <Button size="sm" variant="outline" className="flex-1">
            <Phone className="h-4 w-4 mr-1" />
            Call
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <Mail className="h-4 w-4 mr-1" />
            Email
          </Button>
          <Button size="sm" variant="outline" className="flex-1">
            <Calendar className="h-4 w-4 mr-1" />
            Schedule
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}