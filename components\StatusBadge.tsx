import { Badge } from './ui/badge';

interface StatusBadgeProps {
  status: 'new' | 'contacted' | 'qualified' | 'closed-won' | 'closed-lost' | 'scheduled' | 'completed' | 'cancelled';
  variant?: 'default' | 'secondary' | 'destructive' | 'outline';
  size?: 'sm' | 'default' | 'lg';
  onClick?: () => void;
}

export function StatusBadge({ status, variant = 'secondary', size = 'default', onClick }: StatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'new':
        return { label: 'New', className: 'bg-blue-100 text-blue-800 hover:bg-blue-200' };
      case 'contacted':
        return { label: 'Contacted', className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' };
      case 'qualified':
        return { label: 'Qualified', className: 'bg-green-100 text-green-800 hover:bg-green-200' };
      case 'closed-won':
        return { label: 'Closed Won', className: 'bg-green-500 text-white hover:bg-green-600' };
      case 'closed-lost':
        return { label: 'Closed Lost', className: 'bg-red-100 text-red-800 hover:bg-red-200' };
      case 'scheduled':
        return { label: 'Scheduled', className: 'bg-blue-100 text-blue-800 hover:bg-blue-200' };
      case 'completed':
        return { label: 'Completed', className: 'bg-green-100 text-green-800 hover:bg-green-200' };
      case 'cancelled':
        return { label: 'Cancelled', className: 'bg-red-100 text-red-800 hover:bg-red-200' };
      default:
        return { label: status, className: 'bg-gray-100 text-gray-800 hover:bg-gray-200' };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge 
      variant={variant}
      className={`${config.className} ${onClick ? 'cursor-pointer' : ''} ${
        size === 'sm' ? 'text-xs px-2 py-1' : 
        size === 'lg' ? 'text-sm px-3 py-1' : ''
      }`}
      onClick={onClick}
    >
      {config.label}
    </Badge>
  );
}