'use client'

import { useState } from 'react';
import { ArrowLeft, Phone, Mail, Calendar, Edit } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { StatusBadge } from '../StatusBadge';
import { mockLeads, mockActivities, mockDocuments } from '../../data/mockData';
import { Lead } from '../../types';
import { LeadForm } from './LeadDetail/LeadForm';
import { LeadOverview } from './LeadDetail/LeadOverview';
import { ActivityTimeline } from './LeadDetail/ActivityTimeline';

interface LeadDetailProps {
  leadId: string;
  onNavigate: (path: string) => void;
}

export function LeadDetail({ leadId, onNavigate }: LeadDetailProps) {
  const [lead, setLead] = useState<Lead | null>(
    leadId === 'new' ? null : mockLeads.find(l => l.id === leadId) || null
  );
  const [isEditing, setIsEditing] = useState(leadId === 'new');
  const [formData, setFormData] = useState<Partial<Lead>>(
    lead || {
      name: '',
      email: '',
      phone: '',
      status: 'new',
      source: '',
      propertyInterest: '',
      budget: 0,
      assignedAgent: 'Sarah Johnson',
      notes: ''
    }
  );

  const leadActivities = mockActivities.filter(activity => activity.relatedId === leadId);
  const leadDocuments = mockDocuments.filter(doc => doc.leadId === leadId);

  const handleSave = () => {
    if (leadId === 'new') {
      const newLead: Lead = {
        ...formData as Lead,
        id: Date.now().toString(),
        lastContact: new Date().toISOString().split('T')[0],
        nextAction: 'Initial contact',
        createdAt: new Date().toISOString().split('T')[0]
      };
      setLead(newLead);
      onNavigate(`/leads/${newLead.id}`);
    } else {
      setLead({ ...lead!, ...formData });
    }
    setIsEditing(false);
  };

  if (!lead && leadId !== 'new') {
    return (
      <div className="p-6">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="outline" onClick={() => onNavigate('/leads')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Leads
          </Button>
        </div>
        <div className="text-center py-12">
          <h2>Lead not found</h2>
          <p className="text-muted-foreground">The lead you're looking for doesn't exist.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => onNavigate('/leads')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Leads
            </Button>
            <div>
              <h1>{leadId === 'new' ? 'Add New Lead' : (lead?.name || 'Lead Details')}</h1>
              {lead && <StatusBadge status={lead.status} />}
            </div>
          </div>
          <div className="flex gap-2">
            {!isEditing && lead && (
              <>
                <Button variant="outline">
                  <Phone className="h-4 w-4 mr-2" />
                  Call
                </Button>
                <Button variant="outline">
                  <Mail className="h-4 w-4 mr-2" />
                  Email
                </Button>
                <Button variant="outline">
                  <Calendar className="h-4 w-4 mr-2" />
                  Schedule
                </Button>
              </>
            )}
            {isEditing ? (
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setIsEditing(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSave}>
                  Save Changes
                </Button>
              </div>
            ) : (
              <Button onClick={() => setIsEditing(true)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {isEditing ? (
          <LeadForm formData={formData} onChange={setFormData} />
        ) : (
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList>
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="activity">Activity</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              {lead && <LeadOverview lead={lead} documents={leadDocuments} />}
            </TabsContent>

            <TabsContent value="activity">
              <ActivityTimeline activities={leadActivities} />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </div>
  );
}