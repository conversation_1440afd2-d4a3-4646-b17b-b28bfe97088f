'use client'

import { LeadDetail } from '../../../components/screens/LeadDetail';
import { useRouter } from 'next/navigation';

interface LeadDetailPageProps {
  params: {
    id: string;
  };
}

export default function LeadDetailPage({ params }: LeadDetailPageProps) {
  const router = useRouter();
  
  const handleNavigate = (path: string) => {
    router.push(path);
  };

  return <LeadDetail leadId={params.id} onNavigate={handleNavigate} />;
}