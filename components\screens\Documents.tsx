'use client'

import { useState } from 'react';
import { Upload, Search, FolderPlus, Filter, Grid3X3, List, Download, Share, Trash2, File, FileText, Image, Archive } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Card, CardContent } from '../ui/card';
import { Badge } from '../ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../ui/dropdown-menu';
import { mockDocuments, mockFolders } from '../../data/mockData';
import { Document, Folder } from '../../types';

interface DocumentsProps {
  onNavigate: (path: string) => void;
}

export function Documents({ onNavigate }: DocumentsProps) {
  const [documents, setDocuments] = useState<Document[]>(mockDocuments);
  const [folders, setFolders] = useState<Folder[]>(mockFolders);
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = typeFilter === 'all' || doc.type === typeFilter;
    const matchesFolder = currentFolder ? doc.folderId === currentFolder : !doc.folderId;
    return matchesSearch && matchesType && matchesFolder;
  });

  const currentFolderData = currentFolder ? folders.find(f => f.id === currentFolder) : null;

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'pdf':
        return <FileText className="h-8 w-8 text-red-500" />;
      case 'xlsx':
      case 'xls':
        return <File className="h-8 w-8 text-green-500" />;
      case 'png':
      case 'jpg':
      case 'jpeg':
        return <Image className="h-8 w-8 text-blue-500" />;
      case 'zip':
        return <Archive className="h-8 w-8 text-yellow-500" />;
      default:
        return <File className="h-8 w-8 text-gray-500" />;
    }
  };

  const handleFileUpload = () => {
    // Mock file upload
    const newDoc: Document = {
      id: Date.now().toString(),
      name: 'New_Document.pdf',
      type: 'pdf',
      size: 1024000,
      uploadedAt: new Date().toISOString(),
      folderId: currentFolder || undefined,
      url: '#'
    };
    setDocuments([...documents, newDoc]);
  };

  const handleDeleteDocument = (docId: string) => {
    setDocuments(documents.filter(doc => doc.id !== docId));
  };

  const DocumentCard = ({ document }: { document: Document }) => (
    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer group">
      <CardContent className="p-0">
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            {getFileIcon(document.type)}
          </div>
          <div className="flex-1 min-w-0">
            <h4 className="font-medium text-sm truncate group-hover:text-primary">
              {document.name}
            </h4>
            <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
              <span>{formatFileSize(document.size)}</span>
              <span>•</span>
              <span>{new Date(document.uploadedAt).toLocaleDateString()}</span>
            </div>
            {document.leadId && (
              <Badge variant="secondary" className="mt-2 text-xs">
                Client Document
              </Badge>
            )}
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
                <Upload className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Download className="h-4 w-4 mr-2" />
                Download
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Share className="h-4 w-4 mr-2" />
                Share
              </DropdownMenuItem>
              <DropdownMenuItem 
                onClick={() => handleDeleteDocument(document.id)}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );

  const DocumentRow = ({ document }: { document: Document }) => (
    <div className="flex items-center gap-4 p-3 hover:bg-muted/50 cursor-pointer group border-b">
      <div className="flex-shrink-0">
        {getFileIcon(document.type)}
      </div>
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-sm truncate group-hover:text-primary">
          {document.name}
        </h4>
      </div>
      <div className="text-sm text-muted-foreground">
        {formatFileSize(document.size)}
      </div>
      <div className="text-sm text-muted-foreground">
        {new Date(document.uploadedAt).toLocaleDateString()}
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="opacity-0 group-hover:opacity-100">
            <Upload className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem>
            <Download className="h-4 w-4 mr-2" />
            Download
          </DropdownMenuItem>
          <DropdownMenuItem>
            <Share className="h-4 w-4 mr-2" />
            Share
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => handleDeleteDocument(document.id)}
            className="text-destructive"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );

  return (
    <div className="h-full flex">
      {/* Folder Sidebar */}
      <div className="w-64 border-r bg-muted/20">
        <div className="p-4 border-b">
          <Button className="w-full" size="sm">
            <FolderPlus className="h-4 w-4 mr-2" />
            New Folder
          </Button>
        </div>
        
        <div className="p-2">
          <div 
            className={`p-2 rounded cursor-pointer hover:bg-muted ${!currentFolder ? 'bg-muted' : ''}`}
            onClick={() => setCurrentFolder(null)}
          >
            All Documents
          </div>
          {folders.map(folder => (
            <div
              key={folder.id}
              className={`p-2 rounded cursor-pointer hover:bg-muted ${currentFolder === folder.id ? 'bg-muted' : ''}`}
              onClick={() => setCurrentFolder(folder.id)}
            >
              {folder.name}
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="p-6 border-b">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1>Documents</h1>
              <p className="text-muted-foreground">
                {currentFolderData ? `${currentFolderData.name} folder` : 'Organize and manage your files'}
              </p>
            </div>
            <div className="flex gap-2">
              <Button onClick={handleFileUpload}>
                <Upload className="h-4 w-4 mr-2" />
                Upload Files
              </Button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="flex gap-4 items-center">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="File type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="pdf">PDF</SelectItem>
                <SelectItem value="xlsx">Excel</SelectItem>
                <SelectItem value="png">Images</SelectItem>
                <SelectItem value="zip">Archives</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex border rounded-lg">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-r-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-l-none"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* File Grid/List */}
        <div className="flex-1 overflow-y-auto p-6">
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {filteredDocuments.map(document => (
                <DocumentCard key={document.id} document={document} />
              ))}
            </div>
          ) : (
            <div className="space-y-0">
              {/* List Header */}
              <div className="flex items-center gap-4 p-3 border-b font-medium text-sm text-muted-foreground">
                <div className="w-8"></div>
                <div className="flex-1">Name</div>
                <div className="w-20">Size</div>
                <div className="w-24">Date</div>
                <div className="w-8"></div>
              </div>
              {filteredDocuments.map(document => (
                <DocumentRow key={document.id} document={document} />
              ))}
            </div>
          )}
          
          {filteredDocuments.length === 0 && (
            <div className="flex items-center justify-center h-64 text-muted-foreground">
              <div className="text-center">
                <File className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3>No documents found</h3>
                <p>Upload some files to get started</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}