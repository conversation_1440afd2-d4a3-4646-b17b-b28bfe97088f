export interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: 'new' | 'contacted' | 'qualified' | 'closed-won' | 'closed-lost';
  source: string;
  propertyInterest: string;
  budget: number;
  lastContact: string;
  nextAction: string;
  assignedAgent: string;
  createdAt: string;
  notes: string;
}

export interface OpenHouse {
  id: string;
  title: string;
  propertyAddress: string;
  date: string;
  startTime: string;
  endTime: string;
  agent: string;
  attendees: number;
  status: 'scheduled' | 'completed' | 'cancelled';
  description: string;
}

export interface Message {
  id: string;
  senderId: string;
  recipientId: string;
  content: string;
  timestamp: string;
  type: 'text' | 'email' | 'sms';
  isRead: boolean;
}

export interface Document {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: string;
  folderId?: string;
  leadId?: string;
  propertyId?: string;
  url: string;
}

export interface Folder {
  id: string;
  name: string;
  parentId?: string;
  createdAt: string;
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'agent' | 'admin';
  avatar?: string;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  timestamp: string;
  isRead: boolean;
}

export interface Activity {
  id: string;
  type: 'lead-added' | 'open-house-scheduled' | 'message-sent' | 'document-uploaded';
  description: string;
  timestamp: string;
  userId: string;
  relatedId?: string;
}