'use client'

import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, <PERSON><PERSON>hart, Pie, Cell } from 'recharts';
import { TrendingUp, Users, Calendar, DollarSign, Download, Filter } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { MetricCard } from '../MetricCard';

interface AnalyticsProps {
  onNavigate: (path: string) => void;
}

export function Analytics({ onNavigate }: AnalyticsProps) {
  const leadTrendData = [
    { month: 'Jan', leads: 45, converted: 12 },
    { month: 'Feb', leads: 52, converted: 15 },
    { month: 'Mar', leads: 48, converted: 18 },
    { month: 'Apr', leads: 61, converted: 22 },
    { month: 'May', leads: 55, converted: 19 },
    { month: 'Jun', leads: 67, converted: 25 }
  ];

  const sourceData = [
    { name: 'Website', value: 35, color: '#8884d8' },
    { name: 'Referral', value: 28, color: '#82ca9d' },
    { name: 'Social Media', value: 20, color: '#ffc658' },
    { name: 'Direct', value: 17, color: '#ff7300' }
  ];

  const revenueData = [
    { month: 'Jan', revenue: 125000 },
    { month: 'Feb', revenue: 180000 },
    { month: 'Mar', revenue: 220000 },
    { month: 'Apr', revenue: 310000 },
    { month: 'May', revenue: 280000 },
    { month: 'Jun', revenue: 350000 }
  ];

  const propertyTypeData = [
    { type: 'Condos', count: 45 },
    { type: 'Houses', count: 38 },
    { type: 'Apartments', count: 25 },
    { type: 'Townhomes', count: 18 }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1>Analytics & Reports</h1>
          <p className="text-muted-foreground">Track your performance and gain insights into your business</p>
        </div>
        <div className="flex gap-2">
          <Select defaultValue="30d">
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Leads"
          value="328"
          change={15}
          trend="up"
          icon={<Users className="h-5 w-5" />}
        />
        <MetricCard
          title="Conversion Rate"
          value="18.5%"
          change={3.2}
          trend="up"
          icon={<TrendingUp className="h-5 w-5" />}
        />
        <MetricCard
          title="Open Houses"
          value="24"
          change={-5}
          trend="down"
          icon={<Calendar className="h-5 w-5" />}
        />
        <MetricCard
          title="Revenue (YTD)"
          value="$1.47M"
          change={22}
          trend="up"
          icon={<DollarSign className="h-5 w-5" />}
        />
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Lead Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Lead Generation Trend</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={leadTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Line type="monotone" dataKey="leads" stroke="#2563eb" strokeWidth={2} />
                <Line type="monotone" dataKey="converted" stroke="#8b5cf6" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Lead Sources */}
        <Card>
          <CardHeader>
            <CardTitle>Lead Sources</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={sourceData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {sourceData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Revenue Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Revenue</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={revenueData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`$${Number(value).toLocaleString()}`, 'Revenue']} />
                <Bar dataKey="revenue" fill="#2563eb" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Property Types */}
        <Card>
          <CardHeader>
            <CardTitle>Property Types in Demand</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={propertyTypeData} layout="horizontal">
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis type="number" />
                <YAxis dataKey="type" type="category" />
                <Tooltip />
                <Bar dataKey="count" fill="#8b5cf6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Performance Summary Table */}
      <Card>
        <CardHeader>
          <CardTitle>Performance Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Metric</th>
                  <th className="text-left p-2">This Month</th>
                  <th className="text-left p-2">Last Month</th>
                  <th className="text-left p-2">Change</th>
                  <th className="text-left p-2">Goal</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="p-2">New Leads</td>
                  <td className="p-2">67</td>
                  <td className="p-2">55</td>
                  <td className="p-2 text-green-600">+21.8%</td>
                  <td className="p-2">70</td>
                </tr>
                <tr className="border-b">
                  <td className="p-2">Conversions</td>
                  <td className="p-2">25</td>
                  <td className="p-2">19</td>
                  <td className="p-2 text-green-600">+31.6%</td>
                  <td className="p-2">30</td>
                </tr>
                <tr className="border-b">
                  <td className="p-2">Open Houses</td>
                  <td className="p-2">8</td>
                  <td className="p-2">12</td>
                  <td className="p-2 text-red-600">-33.3%</td>
                  <td className="p-2">15</td>
                </tr>
                <tr>
                  <td className="p-2">Revenue</td>
                  <td className="p-2">$350,000</td>
                  <td className="p-2">$280,000</td>
                  <td className="p-2 text-green-600">+25.0%</td>
                  <td className="p-2">$400,000</td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}