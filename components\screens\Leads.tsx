'use client'

import { useState } from 'react';
import { Search, Plus, Filter, Grid3X3, List, Phone, Mail, Calendar } from 'lucide-react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { LeadCard } from '../LeadCard';
import { StatusBadge } from '../StatusBadge';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { mockLeads } from '../../data/mockData';
import { Lead } from '../../types';

interface LeadsProps {
  onNavigate: (path: string) => void;
}

export function Leads({ onNavigate }: LeadsProps) {
  const [leads, setLeads] = useState<Lead[]>(mockLeads);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'list' | 'kanban'>('list');
  const [selectedLead, setSelectedLead] = useState<Lead | null>(leads[0]);

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = lead.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         lead.phone.includes(searchTerm);
    const matchesStatus = statusFilter === 'all' || lead.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleStatusChange = (leadId: string, newStatus: Lead['status']) => {
    setLeads(leads.map(lead => 
      lead.id === leadId ? { ...lead, status: newStatus } : lead
    ));
  };

  const handleEdit = (leadId: string) => {
    onNavigate(`/leads/${leadId}`);
  };

  const handleDelete = (leadId: string) => {
    setLeads(leads.filter(lead => lead.id !== leadId));
    if (selectedLead?.id === leadId) {
      setSelectedLead(filteredLeads[0] || null);
    }
  };

  const getStatusCounts = () => {
    return leads.reduce((acc, lead) => {
      acc[lead.status] = (acc[lead.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  };

  const statusCounts = getStatusCounts();

  const KanbanColumn = ({ status, title, leads }: { status: Lead['status'], title: string, leads: Lead[] }) => (
    <div className="flex-1 min-w-0">
      <div className="bg-muted/30 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-4">
          <h3 className="font-medium">{title}</h3>
          <Badge variant="secondary">{leads.length}</Badge>
        </div>
        <div className="space-y-3">
          {leads.map(lead => (
            <div key={lead.id} className="cursor-pointer" onClick={() => setSelectedLead(lead)}>
              <LeadCard 
                lead={lead}
                onStatusChange={handleStatusChange}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1>Leads</h1>
            <p className="text-muted-foreground">Manage your sales pipeline and track lead progress</p>
          </div>
          <Button onClick={() => onNavigate('/leads/new')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Lead
          </Button>
        </div>

        {/* Search and Filters */}
        <div className="flex gap-4 items-center">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search leads by name, email, or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="new">New ({statusCounts.new || 0})</SelectItem>
              <SelectItem value="contacted">Contacted ({statusCounts.contacted || 0})</SelectItem>
              <SelectItem value="qualified">Qualified ({statusCounts.qualified || 0})</SelectItem>
              <SelectItem value="closed-won">Closed Won ({statusCounts['closed-won'] || 0})</SelectItem>
              <SelectItem value="closed-lost">Closed Lost ({statusCounts['closed-lost'] || 0})</SelectItem>
            </SelectContent>
          </Select>
          <div className="flex border rounded-lg">
            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="rounded-r-none"
            >
              <List className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'kanban' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('kanban')}
              className="rounded-l-none"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {viewMode === 'list' ? (
          <div className="flex h-full">
            {/* Lead List */}
            <div className="w-1/2 border-r overflow-y-auto p-6 space-y-4">
              {filteredLeads.map(lead => (
                <div 
                  key={lead.id}
                  className={`cursor-pointer ${selectedLead?.id === lead.id ? 'ring-2 ring-primary rounded-lg' : ''}`}
                  onClick={() => setSelectedLead(lead)}
                >
                  <LeadCard 
                    lead={lead}
                    onStatusChange={handleStatusChange}
                    onEdit={handleEdit}
                    onDelete={handleDelete}
                  />
                </div>
              ))}
            </div>

            {/* Detail Panel */}
            <div className="w-1/2 overflow-y-auto">
              {selectedLead ? (
                <div className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <div>
                      <h2>{selectedLead.name}</h2>
                      <StatusBadge status={selectedLead.status} />
                    </div>
                    <Button onClick={() => handleEdit(selectedLead.id)}>
                      View Details
                    </Button>
                  </div>

                  <Card className="mb-6">
                    <CardHeader>
                      <CardTitle>Contact Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="text-sm text-muted-foreground">Email</label>
                          <p>{selectedLead.email}</p>
                        </div>
                        <div>
                          <label className="text-sm text-muted-foreground">Phone</label>
                          <p>{selectedLead.phone}</p>
                        </div>
                        <div>
                          <label className="text-sm text-muted-foreground">Budget</label>
                          <p>${selectedLead.budget.toLocaleString()}</p>
                        </div>
                        <div>
                          <label className="text-sm text-muted-foreground">Source</label>
                          <p>{selectedLead.source}</p>
                        </div>
                      </div>
                      
                      <div>
                        <label className="text-sm text-muted-foreground">Property Interest</label>
                        <p>{selectedLead.propertyInterest}</p>
                      </div>
                      
                      <div>
                        <label className="text-sm text-muted-foreground">Next Action</label>
                        <p>{selectedLead.nextAction}</p>
                      </div>

                      <div className="flex gap-2 pt-4">
                        <Button className="flex-1">
                          <Phone className="h-4 w-4 mr-2" />
                          Call
                        </Button>
                        <Button variant="outline" className="flex-1">
                          <Mail className="h-4 w-4 mr-2" />
                          Email
                        </Button>
                        <Button variant="outline" className="flex-1">
                          <Calendar className="h-4 w-4 mr-2" />
                          Schedule
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ) : (
                <div className="p-6 h-full flex items-center justify-center text-muted-foreground">
                  Select a lead to view details
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="p-6">
            <div className="flex gap-6 overflow-x-auto pb-6">
              <KanbanColumn 
                status="new"
                title="New Leads" 
                leads={filteredLeads.filter(l => l.status === 'new')} 
              />
              <KanbanColumn 
                status="contacted"
                title="Contacted" 
                leads={filteredLeads.filter(l => l.status === 'contacted')} 
              />
              <KanbanColumn 
                status="qualified"
                title="Qualified" 
                leads={filteredLeads.filter(l => l.status === 'qualified')} 
              />
              <KanbanColumn 
                status="closed-won"
                title="Closed Won" 
                leads={filteredLeads.filter(l => l.status === 'closed-won')} 
              />
              <KanbanColumn 
                status="closed-lost"
                title="Closed Lost" 
                leads={filteredLeads.filter(l => l.status === 'closed-lost')} 
              />
            </div>
          </div>
        )}
      </div>
    </div>
  );
}