import { Plus, MessageSquare, FileText, Calendar } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Activity } from '../../../types';

interface ActivityTimelineProps {
  activities: Activity[];
}

export function ActivityTimeline({ activities }: ActivityTimelineProps) {
  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'lead-added':
        return <Plus className="h-4 w-4 text-blue-600" />;
      case 'message-sent':
        return <MessageSquare className="h-4 w-4 text-purple-600" />;
      case 'document-uploaded':
        return <FileText className="h-4 w-4 text-orange-600" />;
      default:
        return <Calendar className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Activity Timeline</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start gap-3 pb-4 border-b last:border-b-0">
              <div className="mt-1">
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1">
                <p className="text-sm">{activity.description}</p>
                <p className="text-xs text-muted-foreground mt-1">
                  {new Date(activity.timestamp).toLocaleString()}
                </p>
              </div>
            </div>
          ))}
          {activities.length === 0 && (
            <p className="text-muted-foreground text-center py-8">No activity recorded yet</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}