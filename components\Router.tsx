import { useState } from 'react';
import { Dashboard } from './screens/Dashboard';
import { Leads } from './screens/Leads';
import { OpenHouses } from './screens/OpenHouses';
import { Communications } from './screens/Communications';
import { Documents } from './screens/Documents';
import { Analytics } from './screens/Analytics';
import { LeadDetail } from './screens/LeadDetail';
import { Settings } from './screens/Settings';

export type Route = 
  | { type: 'dashboard' }
  | { type: 'leads' }
  | { type: 'open-houses' }
  | { type: 'communications' }
  | { type: 'documents' }
  | { type: 'analytics' }
  | { type: 'lead-detail', leadId: string }
  | { type: 'settings' };

interface RouterProps {
  currentRoute: Route;
  onNavigate: (route: Route) => void;
}

export function Router({ currentRoute, onNavigate }: RouterProps) {
  // Convert Route object navigation to path string navigation
  const handleNavigateWithPath = (path: string) => {
    // Convert path to Route object
    if (path === '/') {
      onNavigate({ type: 'dashboard' });
    } else if (path === '/leads') {
      onNavigate({ type: 'leads' });
    } else if (path.startsWith('/leads/') && path !== '/leads') {
      const leadId = path.split('/')[2];
      onNavigate({ type: 'lead-detail', leadId });
    } else if (path === '/open-houses') {
      onNavigate({ type: 'open-houses' });
    } else if (path === '/communications') {
      onNavigate({ type: 'communications' });
    } else if (path === '/documents') {
      onNavigate({ type: 'documents' });
    } else if (path === '/analytics') {
      onNavigate({ type: 'analytics' });
    } else if (path === '/settings') {
      onNavigate({ type: 'settings' });
    }
  };

  switch (currentRoute.type) {
    case 'dashboard':
      return <Dashboard onNavigate={handleNavigateWithPath} />;
    case 'leads':
      return <Leads onNavigate={handleNavigateWithPath} />;
    case 'open-houses':
      return <OpenHouses onNavigate={handleNavigateWithPath} />;
    case 'communications':
      return <Communications onNavigate={handleNavigateWithPath} />;
    case 'documents':
      return <Documents onNavigate={handleNavigateWithPath} />;
    case 'analytics':
      return <Analytics onNavigate={handleNavigateWithPath} />;
    case 'lead-detail':
      return <LeadDetail leadId={currentRoute.leadId} onNavigate={handleNavigateWithPath} />;
    case 'settings':
      return <Settings onNavigate={handleNavigateWithPath} />;
    default:
      return <Dashboard onNavigate={handleNavigateWithPath} />;
  }
}