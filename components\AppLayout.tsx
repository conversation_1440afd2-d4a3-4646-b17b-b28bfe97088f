'use client'

import { useState } from 'react';
import { usePathname } from 'next/navigation';
import { NavigationSidebar } from './NavigationSidebar';
import { NotificationPanel } from './NotificationPanel';
import { mockNotifications } from '../data/mockData';
import { Notification } from '../types';

interface AppLayoutProps {
  children: React.ReactNode;
}

export function AppLayout({ children }: AppLayoutProps) {
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const pathname = usePathname();

  const handleDismissNotification = (notificationId: string) => {
    setNotifications(notifications.filter(n => n.id !== notificationId));
  };

  const handleNotificationAction = (notificationId: string, action: string) => {
    if (action === 'view') {
      const notification = notifications.find(n => n.id === notificationId);
      if (notification) {
        // Navigate based on notification type
        if (notification.title === 'New Lead') {
          window.location.href = '/leads';
        } else if (notification.title === 'Open House Reminder') {
          window.location.href = '/open-houses';
        }
      }
    }
    handleDismissNotification(notificationId);
  };

  const unreadNotificationCount = notifications.filter(n => !n.isRead).length;

  return (
    <div className="h-screen flex bg-background">
      {/* Sidebar */}
      <NavigationSidebar
        currentPath={pathname}
        collapsed={sidebarCollapsed}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        unreadCount={unreadNotificationCount}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {children}
      </div>

      {/* Notifications */}
      <NotificationPanel
        notifications={notifications.filter(n => !n.isRead)}
        maxVisible={3}
        autoHide={false}
        onDismiss={handleDismissNotification}
        onAction={handleNotificationAction}
        onViewAll={() => window.location.href = '/settings'}
      />
    </div>
  );
}