'use client'

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Users, Calendar, MessageSquare, FolderOpen, BarChart3, Settings, Bell } from 'lucide-react';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { mockUser } from '../data/mockData';
import { Route } from './Router';

interface NavigationSidebarProps {
  // New Next.js props
  currentPath?: string;
  // Old React Router props  
  activeRoute?: Route;
  onNavigate?: (route: Route) => void;
  collapsed?: boolean;
  onToggle?: () => void;
  onLogout?: () => void;
  unreadCount?: number;
}

interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  badge?: number;
  href: string;
  route: Route;
}

export function NavigationSidebar({ 
  currentPath,
  activeRoute,
  onNavigate,
  collapsed = false, 
  onToggle, 
  onLogout,
  unreadCount = 0 
}: NavigationSidebarProps) {
  
  // Try to get pathname from Next.js hook, but fallback gracefully
  let pathname = currentPath;
  try {
    if (!pathname && typeof window !== 'undefined') {
      pathname = usePathname();
    }
  } catch (e) {
    // usePathname not available in non-Next.js context
  }

  const navItems: NavItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <Home className="h-4 w-4" />,
      href: '/',
      route: { type: 'dashboard' }
    },
    {
      id: 'leads',
      label: 'Leads',
      icon: <Users className="h-4 w-4" />,
      badge: 3,
      href: '/leads',
      route: { type: 'leads' }
    },
    {
      id: 'open-houses',
      label: 'Open Houses',
      icon: <Calendar className="h-4 w-4" />,
      href: '/open-houses',
      route: { type: 'open-houses' }
    },
    {
      id: 'communications',
      label: 'Communications',
      icon: <MessageSquare className="h-4 w-4" />,
      badge: unreadCount > 0 ? unreadCount : undefined,
      href: '/communications',
      route: { type: 'communications' }
    },
    {
      id: 'documents',
      label: 'Documents',
      icon: <FolderOpen className="h-4 w-4" />,
      href: '/documents',
      route: { type: 'documents' }
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: <BarChart3 className="h-4 w-4" />,
      href: '/analytics',
      route: { type: 'analytics' }
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="h-4 w-4" />,
      href: '/settings',
      route: { type: 'settings' }
    }
  ];

  const isActive = (item: NavItem) => {
    // If we have currentPath (Next.js mode), use pathname matching
    if (pathname) {
      if (item.href === '/') {
        return pathname === '/';
      }
      return pathname.startsWith(item.href);
    }
    
    // If we have activeRoute (old Router mode), use route matching
    if (activeRoute) {
      return activeRoute.type === item.route.type;
    }
    
    return false;
  };

  const handleItemClick = (item: NavItem) => {
    if (onNavigate) {
      // Old Router mode
      onNavigate(item.route);
    } else if (typeof window !== 'undefined') {
      // Fallback to window navigation
      window.location.href = item.href;
    }
  };

  const NavButton = ({ item }: { item: NavItem }) => {
    const buttonContent = (
      <Button
        variant={isActive(item) ? "default" : "ghost"}
        className={`w-full justify-start gap-3 ${collapsed ? 'px-2' : 'px-3'} ${
          isActive(item) 
            ? 'bg-sidebar-primary text-sidebar-primary-foreground' 
            : 'text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground'
        }`}
        onClick={() => handleItemClick(item)}
      >
        {item.icon}
        {!collapsed && (
          <>
            <span className="flex-1 text-left">{item.label}</span>
            {item.badge && (
              <Badge variant="secondary" className="ml-auto">
                {item.badge}
              </Badge>
            )}
          </>
        )}
      </Button>
    );

    // If we're in Next.js mode and have working Link, use it
    if (pathname && typeof window !== 'undefined' && window.next) {
      return (
        <Link href={item.href} key={item.id}>
          {buttonContent}
        </Link>
      );
    }

    // Otherwise just return the button
    return <div key={item.id}>{buttonContent}</div>;
  };

  return (
    <div className={`bg-sidebar border-r border-sidebar-border h-full flex flex-col transition-all duration-300 ${collapsed ? 'w-16' : 'w-64'}`}>
      {/* Header */}
      <div className="p-4 border-b border-sidebar-border">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <Home className="h-4 w-4 text-primary-foreground" />
          </div>
          {!collapsed && (
            <div>
              <h1 className="font-semibold text-sidebar-foreground">CRM Lite</h1>
              <p className="text-xs text-sidebar-foreground/60">Real Estate</p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-2 space-y-1">
        {navItems.map((item) => (
          <NavButton key={item.id} item={item} />
        ))}
      </nav>

      {/* User Profile */}
      <div className="p-4 border-t border-sidebar-border">
        <div className={`flex items-center gap-3 ${collapsed ? 'justify-center' : ''}`}>
          <Avatar className="h-8 w-8">
            <AvatarImage src={mockUser.avatar} alt={mockUser.name} />
            <AvatarFallback>{mockUser.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
          </Avatar>
          {!collapsed && (
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-sidebar-foreground truncate">{mockUser.name}</p>
              <p className="text-xs text-sidebar-foreground/60 truncate">{mockUser.email}</p>
            </div>
          )}
        </div>
        {!collapsed && onLogout && (
          <Button 
            variant="ghost" 
            size="sm" 
            className="w-full mt-2 text-sidebar-foreground hover:bg-sidebar-accent"
            onClick={onLogout}
          >
            Sign out
          </Button>
        )}
      </div>
    </div>
  );
}