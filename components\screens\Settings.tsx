'use client'

import { useState } from 'react';
import { Save, User, Bell, Link, Database, Upload } from 'lucide-react';
import { Button } from '../ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Input } from '../ui/input';
import { Switch } from '../ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { mockUser } from '../../data/mockData';

interface SettingsProps {
  onNavigate: (path: string) => void;
}

export function Settings({ onNavigate }: SettingsProps) {
  const [profile, setProfile] = useState(mockUser);
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    smsNotifications: false,
    leadAlerts: true,
    openHouseReminders: true,
    taskDeadlines: true
  });

  const [integrations, setIntegrations] = useState({
    googleCalendar: false,
    outlook: false,
    zapier: false,
    mailchimp: false
  });

  const handleSaveProfile = () => {
    // Mock save action
    console.log('Profile saved:', profile);
  };

  const handleExportData = () => {
    // Mock export action
    console.log('Exporting data...');
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1>Settings</h1>
          <p className="text-muted-foreground">Manage your account and application preferences</p>
        </div>
      </div>

      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList>
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="integrations">Integrations</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Profile Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center gap-6">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={profile.avatar} alt={profile.name} />
                  <AvatarFallback className="text-lg">
                    {profile.name.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Change Photo
                  </Button>
                  <p className="text-sm text-muted-foreground mt-2">
                    JPG, PNG or GIF. Max size 2MB.
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Full Name</label>
                  <Input
                    value={profile.name}
                    onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Email</label>
                  <Input
                    type="email"
                    value={profile.email}
                    onChange={(e) => setProfile({ ...profile, email: e.target.value })}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Role</label>
                  <Input value={profile.role} disabled />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Phone</label>
                  <Input placeholder="Enter phone number" />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Bio</label>
                <Input placeholder="Tell us about yourself" />
              </div>

              <Button onClick={handleSaveProfile}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5" />
                Notification Preferences
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h4>Email Notifications</h4>
                    <p className="text-sm text-muted-foreground">
                      Receive notifications via email
                    </p>
                  </div>
                  <Switch
                    checked={notifications.emailNotifications}
                    onCheckedChange={(checked) =>
                      setNotifications({ ...notifications, emailNotifications: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4>SMS Notifications</h4>
                    <p className="text-sm text-muted-foreground">
                      Receive urgent notifications via SMS
                    </p>
                  </div>
                  <Switch
                    checked={notifications.smsNotifications}
                    onCheckedChange={(checked) =>
                      setNotifications({ ...notifications, smsNotifications: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4>Lead Alerts</h4>
                    <p className="text-sm text-muted-foreground">
                      Get notified when new leads are added
                    </p>
                  </div>
                  <Switch
                    checked={notifications.leadAlerts}
                    onCheckedChange={(checked) =>
                      setNotifications({ ...notifications, leadAlerts: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4>Open House Reminders</h4>
                    <p className="text-sm text-muted-foreground">
                      Reminders for upcoming open houses
                    </p>
                  </div>
                  <Switch
                    checked={notifications.openHouseReminders}
                    onCheckedChange={(checked) =>
                      setNotifications({ ...notifications, openHouseReminders: checked })
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <h4>Task Deadlines</h4>
                    <p className="text-sm text-muted-foreground">
                      Notifications for upcoming task deadlines
                    </p>
                  </div>
                  <Switch
                    checked={notifications.taskDeadlines}
                    onCheckedChange={(checked) =>
                      setNotifications({ ...notifications, taskDeadlines: checked })
                    }
                  />
                </div>
              </div>

              <Button>
                <Save className="h-4 w-4 mr-2" />
                Save Preferences
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="integrations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Link className="h-5 w-5" />
                Third-party Integrations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4>Google Calendar</h4>
                    <p className="text-sm text-muted-foreground">
                      Sync open houses and appointments
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={integrations.googleCalendar}
                      onCheckedChange={(checked) =>
                        setIntegrations({ ...integrations, googleCalendar: checked })
                      }
                    />
                    <Button variant="outline" size="sm">
                      Configure
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4>Microsoft Outlook</h4>
                    <p className="text-sm text-muted-foreground">
                      Sync with Outlook calendar and contacts
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={integrations.outlook}
                      onCheckedChange={(checked) =>
                        setIntegrations({ ...integrations, outlook: checked })
                      }
                    />
                    <Button variant="outline" size="sm">
                      Configure
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4>Zapier</h4>
                    <p className="text-sm text-muted-foreground">
                      Connect with 3000+ apps via Zapier
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={integrations.zapier}
                      onCheckedChange={(checked) =>
                        setIntegrations({ ...integrations, zapier: checked })
                      }
                    />
                    <Button variant="outline" size="sm">
                      Configure
                    </Button>
                  </div>
                </div>

                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <h4>Mailchimp</h4>
                    <p className="text-sm text-muted-foreground">
                      Sync leads with email marketing campaigns
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={integrations.mailchimp}
                      onCheckedChange={(checked) =>
                        setIntegrations({ ...integrations, mailchimp: checked })
                      }
                    />
                    <Button variant="outline" size="sm">
                      Configure
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Data & Account Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="p-4 border rounded-lg">
                  <h4 className="mb-2">Export Data</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Download all your data including leads, documents, and activity history.
                  </p>
                  <Button variant="outline" onClick={handleExportData}>
                    Export All Data
                  </Button>
                </div>

                <div className="p-4 border rounded-lg">
                  <h4 className="mb-2">Data Backup</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    Automatically backup your data to cloud storage.
                  </p>
                  <div className="flex items-center gap-4">
                    <Switch defaultChecked />
                    <span className="text-sm">Enable automatic backups</span>
                  </div>
                </div>

                <div className="p-4 border rounded-lg border-destructive">
                  <h4 className="mb-2 text-destructive">Danger Zone</h4>
                  <p className="text-sm text-muted-foreground mb-4">
                    These actions cannot be undone. Please proceed with caution.
                  </p>
                  <div className="space-y-2">
                    <Button variant="outline" className="w-full">
                      Reset All Settings
                    </Button>
                    <Button variant="destructive" className="w-full">
                      Delete Account
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}