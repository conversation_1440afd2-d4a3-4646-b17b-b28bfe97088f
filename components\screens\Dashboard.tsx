'use client'

import { Users, Calendar, CheckSquare, DollarSign, Plus, MessageSquare } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { MetricCard } from '../MetricCard';
import { mockMetrics, mockActivities, mockLeads, mockOpenHouses } from '../../data/mockData';
import { Activity } from '../../types';

interface DashboardProps {
  onNavigate: (path: string) => void;
}

export function Dashboard({ onNavigate }: DashboardProps) {
  const formatActivityTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.abs(now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)}h ago`;
    } else {
      return `${Math.floor(diffInHours / 24)}d ago`;
    }
  };

  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'lead-added':
        return <Users className="h-4 w-4 text-blue-600" />;
      case 'open-house-scheduled':
        return <Calendar className="h-4 w-4 text-green-600" />;
      case 'message-sent':
        return <MessageSquare className="h-4 w-4 text-purple-600" />;
      case 'document-uploaded':
        return <CheckSquare className="h-4 w-4 text-orange-600" />;
      default:
        return <CheckSquare className="h-4 w-4 text-gray-600" />;
    }
  };

  const upcomingAppointments = mockOpenHouses
    .filter(oh => new Date(oh.date) >= new Date())
    .slice(0, 3);

  const pendingTasks = [
    { id: '1', title: 'Follow up with John Smith', dueDate: '2025-01-27' },
    { id: '2', title: 'Prepare Downtown Condo materials', dueDate: '2025-01-28' },
    { id: '3', title: 'Schedule property viewing for Emily Davis', dueDate: '2025-01-29' }
  ];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1>Good morning, Sarah!</h1>
          <p className="text-muted-foreground">Here's what's happening with your business today.</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => onNavigate('/leads')}>
            <Plus className="h-4 w-4 mr-2" />
            Add Lead
          </Button>
          <Button variant="outline" onClick={() => onNavigate('/open-houses')}>
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Open House
          </Button>
        </div>
      </div>

      {/* Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Leads"
          value={mockMetrics.totalLeads}
          change={12}
          trend="up"
          icon={<Users className="h-5 w-5" />}
          onClick={() => onNavigate('/leads')}
        />
        <MetricCard
          title="Open Houses This Week"
          value={mockMetrics.openHousesThisWeek}
          change={25}
          trend="up"
          icon={<Calendar className="h-5 w-5" />}
          onClick={() => onNavigate('/open-houses')}
        />
        <MetricCard
          title="Pending Tasks"
          value={mockMetrics.pendingTasks}
          change={-8}
          trend="down"
          icon={<CheckSquare className="h-5 w-5" />}
        />
        <MetricCard
          title="Revenue Pipeline"
          value={mockMetrics.revenuePipeline}
          change={18}
          trend="up"
          icon={<DollarSign className="h-5 w-5" />}
          onClick={() => onNavigate('/analytics')}
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity Feed */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Recent Activity</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {mockActivities.slice(0, 5).map((activity) => (
                <div key={activity.id} className="flex items-start gap-3">
                  <div className="mt-1">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm">{activity.description}</p>
                    <p className="text-xs text-muted-foreground">
                      {formatActivityTime(activity.timestamp)}
                    </p>
                  </div>
                </div>
              ))}
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={() => onNavigate('/analytics')}
              >
                View all activity
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Appointments */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Appointments</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {upcomingAppointments.map((appointment) => (
                <div key={appointment.id} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                  <Calendar className="h-4 w-4 text-primary" />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium">{appointment.title}</p>
                    <p className="text-xs text-muted-foreground">
                      {new Date(appointment.date).toLocaleDateString()} at {appointment.startTime}
                    </p>
                    <p className="text-xs text-muted-foreground">{appointment.propertyAddress}</p>
                  </div>
                </div>
              ))}
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={() => onNavigate('/open-houses')}
              >
                View calendar
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Task List */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle>Pending Tasks</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {pendingTasks.map((task) => (
                <div key={task.id} className="flex items-center gap-3">
                  <input 
                    type="checkbox" 
                    className="rounded border-gray-300"
                  />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm">{task.title}</p>
                    <p className="text-xs text-muted-foreground">
                      Due: {new Date(task.dueDate).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={() => onNavigate('/leads')}
              >
                View all tasks
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button 
              variant="outline" 
              className="h-20 flex-col gap-2"
              onClick={() => onNavigate('/leads')}
            >
              <Plus className="h-6 w-6" />
              Add New Lead
            </Button>
            <Button 
              variant="outline" 
              className="h-20 flex-col gap-2"
              onClick={() => onNavigate('/open-houses')}
            >
              <Calendar className="h-6 w-6" />
              Schedule Open House
            </Button>
            <Button 
              variant="outline" 
              className="h-20 flex-col gap-2"
              onClick={() => onNavigate('/communications')}
            >
              <MessageSquare className="h-6 w-6" />
              Send Message
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}