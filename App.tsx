import { useState } from 'react';
import { NavigationSidebar } from './components/NavigationSidebar';
import { Router, Route } from './components/Router';
import { NotificationPanel } from './components/NotificationPanel';
import { mockNotifications } from './data/mockData';
import { Notification } from './types';

export default function App() {
  const [currentRoute, setCurrentRoute] = useState<Route>({ type: 'dashboard' });
  const [notifications, setNotifications] = useState<Notification[]>(mockNotifications);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const handleNavigate = (route: Route) => {
    setCurrentRoute(route);
  };

  const handleDismissNotification = (notificationId: string) => {
    setNotifications(notifications.filter(n => n.id !== notificationId));
  };

  const handleNotificationAction = (notificationId: string, action: string) => {
    if (action === 'view') {
      const notification = notifications.find(n => n.id === notificationId);
      if (notification) {
        // Navigate based on notification type
        if (notification.title === 'New Lead') {
          handleNavigate({ type: 'leads' });
        } else if (notification.title === 'Open House Reminder') {
          handleNavigate({ type: 'open-houses' });
        }
      }
    }
    handleDismissNotification(notificationId);
  };

  const unreadNotificationCount = notifications.filter(n => !n.isRead).length;

  return (
    <div className="h-screen flex bg-background">
      {/* Sidebar */}
      <NavigationSidebar
        activeRoute={currentRoute}
        collapsed={sidebarCollapsed}
        onNavigate={handleNavigate}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        unreadCount={unreadNotificationCount}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <Router currentRoute={currentRoute} onNavigate={handleNavigate} />
      </div>

      {/* Notifications */}
      <NotificationPanel
        notifications={notifications.filter(n => !n.isRead)}
        maxVisible={3}
        autoHide={false}
        onDismiss={handleDismissNotification}
        onAction={handleNotificationAction}
        onViewAll={() => handleNavigate({ type: 'settings' })}
      />
    </div>
  );
}