# Real Estate CRM Lite

A streamlined CRM platform for real estate professionals built with Next.js 14, TypeScript, and Tailwind CSS.

## Features

- **Dashboard**: Overview of key metrics and recent activities
- **Lead Management**: Track and manage potential clients through the sales pipeline
- **Open House Scheduling**: Calendar-based scheduling with automated reminders
- **Client Communications**: Centralized messaging and email management
- **Document Storage**: Organized file management system
- **Analytics & Reporting**: Performance insights and data visualization
- **Settings & Configuration**: User preferences and system settings

## Prerequisites

Before you begin, ensure you have the following installed on your system:

- **Node.js** (version 18.0 or higher)
- **npm** (comes with Node.js) or **yarn** package manager
- **Git** (for version control)

## Installation

1. **Clone or download the project**
   ```bash
   git clone <your-repository-url>
   cd real-estate-crm-lite
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000) to see the application.

## Development

- `npm run dev` - Start the development server
- `npm run build` - Build the application for production
- `npm run start` - Start the production server
- `npm run lint` - Run ESLint to check for code quality issues

## Project Structure

```
├── app/                    # Next.js App Router pages
│   ├── layout.tsx         # Root layout component
│   ├── page.tsx           # Dashboard page
│   ├── leads/             # Lead management pages
│   ├── open-houses/       # Open house scheduling
│   ├── communications/    # Client communications
│   ├── documents/         # Document management
│   ├── analytics/         # Analytics and reporting
│   └── settings/          # Application settings
├── components/            # Reusable React components
│   ├── ui/               # Shadcn/UI components
│   ├── screens/          # Page-specific components
│   └── ...               # Other shared components
├── data/                 # Mock data and constants
├── styles/               # Global CSS and Tailwind config
├── types/                # TypeScript type definitions
└── ...
```

## Technology Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **UI Components**: Shadcn/UI + Radix UI
- **Icons**: Lucide React
- **Charts**: Recharts
- **Forms**: React Hook Form

## Browser Support

This application works on all modern browsers including:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.