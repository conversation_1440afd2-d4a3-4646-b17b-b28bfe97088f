{"name": "real-estate-crm-lite", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.0.3", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/postcss": "^4.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "cmdk": "^0.2.1", "date-fns": "^2.30.0", "embla-carousel-react": "^8.0.0", "input-otp": "^1.4.2", "lucide-react": "^0.263.1", "next": "14.0.0", "next-themes": "^0.4.6", "react": "^18.2.0", "react-day-picker": "^8.8.0", "react-dom": "^18.2.0", "react-hook-form": "^7.61.1", "react-resizable-panels": "^0.0.55", "recharts": "^2.8.0", "sonner": "^1.7.4", "tailwind-merge": "^1.14.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.7.0"}, "devDependencies": {"@types/node": "^20.5.2", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "autoprefixer": "^10.4.15", "eslint": "^8.47.0", "eslint-config-next": "14.0.0", "postcss": "^8.4.27", "tailwindcss": "^4.0.0-alpha.26", "typescript": "^5.1.6"}}