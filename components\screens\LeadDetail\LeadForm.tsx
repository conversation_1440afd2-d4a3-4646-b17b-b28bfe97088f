import { Input } from '../../ui/input';
import { Textarea } from '../../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Lead } from '../../../types';

interface LeadFormProps {
  formData: Partial<Lead>;
  onChange: (data: Partial<Lead>) => void;
}

export function LeadForm({ formData, onChange }: LeadFormProps) {
  return (
    <div className="max-w-2xl space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Lead Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Name *</label>
              <Input
                value={formData.name || ''}
                onChange={(e) => onChange({ ...formData, name: e.target.value })}
                placeholder="Enter full name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Status</label>
              <Select 
                value={formData.status} 
                onValueChange={(value) => onChange({ ...formData, status: value as Lead['status'] })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="contacted">Contacted</SelectItem>
                  <SelectItem value="qualified">Qualified</SelectItem>
                  <SelectItem value="closed-won">Closed Won</SelectItem>
                  <SelectItem value="closed-lost">Closed Lost</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Email *</label>
              <Input
                type="email"
                value={formData.email || ''}
                onChange={(e) => onChange({ ...formData, email: e.target.value })}
                placeholder="Enter email address"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Phone *</label>
              <Input
                value={formData.phone || ''}
                onChange={(e) => onChange({ ...formData, phone: e.target.value })}
                placeholder="Enter phone number"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Source</label>
              <Select 
                value={formData.source} 
                onValueChange={(value) => onChange({ ...formData, source: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select source" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Website">Website</SelectItem>
                  <SelectItem value="Referral">Referral</SelectItem>
                  <SelectItem value="Social Media">Social Media</SelectItem>
                  <SelectItem value="Direct">Direct</SelectItem>
                  <SelectItem value="Advertisement">Advertisement</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Budget</label>
              <Input
                type="number"
                value={formData.budget || ''}
                onChange={(e) => onChange({ ...formData, budget: Number(e.target.value) })}
                placeholder="Enter budget"
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Property Interest</label>
            <Input
              value={formData.propertyInterest || ''}
              onChange={(e) => onChange({ ...formData, propertyInterest: e.target.value })}
              placeholder="Describe property requirements"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Notes</label>
            <Textarea
              value={formData.notes || ''}
              onChange={(e) => onChange({ ...formData, notes: e.target.value })}
              placeholder="Add any additional notes..."
              rows={4}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}