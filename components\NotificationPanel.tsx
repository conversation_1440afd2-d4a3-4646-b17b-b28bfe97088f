import { useState, useEffect } from 'react';
import { Bell, X, Check, AlertTriangle, Info, AlertCircle } from 'lucide-react';
import { Button } from './ui/button';
import { Card, CardContent } from './ui/card';
import { Notification } from '../types';

interface NotificationPanelProps {
  notifications: Notification[];
  maxVisible?: number;
  autoHide?: boolean;
  onDismiss: (notificationId: string) => void;
  onAction?: (notificationId: string, action: string) => void;
  onViewAll?: () => void;
}

export function NotificationPanel({ 
  notifications, 
  maxVisible = 5, 
  autoHide = false,
  onDismiss,
  onAction,
  onViewAll 
}: NotificationPanelProps) {
  const [visibleNotifications, setVisibleNotifications] = useState<Notification[]>(notifications.slice(0, maxVisible));

  useEffect(() => {
    setVisibleNotifications(notifications.slice(0, maxVisible));
  }, [notifications, maxVisible]);

  useEffect(() => {
    if (autoHide) {
      const timers = visibleNotifications.map(notification => 
        setTimeout(() => {
          onDismiss(notification.id);
        }, 5000)
      );

      return () => {
        timers.forEach(timer => clearTimeout(timer));
      };
    }
  }, [visibleNotifications, autoHide, onDismiss]);

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <Check className="h-4 w-4 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return <Info className="h-4 w-4 text-blue-600" />;
    }
  };

  const getNotificationBorderColor = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return 'border-l-green-500';
      case 'warning':
        return 'border-l-yellow-500';
      case 'error':
        return 'border-l-red-500';
      default:
        return 'border-l-blue-500';
    }
  };

  if (visibleNotifications.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 w-80">
      {visibleNotifications.map((notification) => (
        <Card 
          key={notification.id}
          className={`border-l-4 ${getNotificationBorderColor(notification.type)} shadow-md`}
        >
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              {getNotificationIcon(notification.type)}
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-sm">{notification.title}</h4>
                <p className="text-sm text-muted-foreground mt-1">{notification.message}</p>
                <p className="text-xs text-muted-foreground mt-2">
                  {new Date(notification.timestamp).toLocaleTimeString()}
                </p>
                {onAction && (
                  <div className="flex gap-2 mt-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => onAction(notification.id, 'view')}
                    >
                      View
                    </Button>
                  </div>
                )}
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-6 w-6 p-0"
                onClick={() => onDismiss(notification.id)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      ))}
      
      {notifications.length > maxVisible && onViewAll && (
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full"
          onClick={onViewAll}
        >
          <Bell className="h-4 w-4 mr-2" />
          View all {notifications.length} notifications
        </Button>
      )}
    </div>
  );
}