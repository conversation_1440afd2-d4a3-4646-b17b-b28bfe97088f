import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Lead, Document } from '../../../types';
import { Button } from '../../ui/button';

interface LeadOverviewProps {
  lead: Lead;
  documents: Document[];
}

export function LeadOverview({ lead, documents }: LeadOverviewProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm text-muted-foreground">Email</label>
              <p>{lead.email}</p>
            </div>
            <div>
              <label className="text-sm text-muted-foreground">Phone</label>
              <p>{lead.phone}</p>
            </div>
            <div>
              <label className="text-sm text-muted-foreground">Source</label>
              <p>{lead.source}</p>
            </div>
            <div>
              <label className="text-sm text-muted-foreground">Assigned Agent</label>
              <p>{lead.assignedAgent}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Property Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm text-muted-foreground">Interest</label>
              <p>{lead.propertyInterest}</p>
            </div>
            <div>
              <label className="text-sm text-muted-foreground">Budget</label>
              <p>{lead.budget ? formatCurrency(lead.budget) : 'Not specified'}</p>
            </div>
            <div>
              <label className="text-sm text-muted-foreground">Last Contact</label>
              <p>{lead.lastContact ? new Date(lead.lastContact).toLocaleDateString() : 'Never'}</p>
            </div>
            <div>
              <label className="text-sm text-muted-foreground">Next Action</label>
              <p>{lead.nextAction}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {lead.notes && (
        <Card>
          <CardHeader>
            <CardTitle>Notes</CardTitle>
          </CardHeader>
          <CardContent>
            <p>{lead.notes}</p>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Documents</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {documents.map((document) => (
              <div key={document.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">{document.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(document.uploadedAt).toLocaleDateString()}
                  </p>
                </div>
                <Button variant="outline" size="sm">
                  Download
                </Button>
              </div>
            ))}
            {documents.length === 0 && (
              <p className="text-muted-foreground text-center py-8">No documents uploaded yet</p>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}